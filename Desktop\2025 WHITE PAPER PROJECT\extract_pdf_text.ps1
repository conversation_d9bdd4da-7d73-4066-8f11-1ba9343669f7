# PowerShell script to extract text from PDF files
# This script attempts to use various methods to extract text from PDFs

param(
    [Parameter(Mandatory=$true)]
    [string]$PdfPath,
    [Parameter(Mandatory=$false)]
    [string]$OutputPath
)

Write-Host "Attempting to extract text from: $PdfPath"

# Method 1: Try using Word COM object (if Office is installed)
try {
    Write-Host "Trying Microsoft Word COM object..."
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    $doc = $word.Documents.Open($PdfPath)
    $text = $doc.Content.Text
    $doc.Close()
    $word.Quit()
    
    if ($OutputPath) {
        $text | Out-File -FilePath $OutputPath -Encoding UTF8
        Write-Host "Text extracted to: $OutputPath"
    } else {
        Write-Host "Extracted text:"
        Write-Host $text
    }
    exit 0
} catch {
    Write-Host "Word COM method failed: $($_.Exception.Message)"
}

# Method 2: Try using Adobe Reader COM (if installed)
try {
    Write-Host "Trying Adobe Reader COM object..."
    $reader = New-Object -ComObject AcroExch.PDDoc
    if ($reader.Open($PdfPath)) {
        $jsObj = $reader.GetJSObject()
        $text = $jsObj.getPageNthWord(0, 0, -1)
        $reader.Close()
        
        if ($OutputPath) {
            $text | Out-File -FilePath $OutputPath -Encoding UTF8
            Write-Host "Text extracted to: $OutputPath"
        } else {
            Write-Host "Extracted text:"
            Write-Host $text
        }
        exit 0
    }
} catch {
    Write-Host "Adobe Reader COM method failed: $($_.Exception.Message)"
}

Write-Host "Could not extract text using available methods."
Write-Host "Please consider:"
Write-Host "1. Installing Python with PyPDF2 or pdfplumber"
Write-Host "2. Using online PDF to text converters"
Write-Host "3. Copying text manually from the PDF"
