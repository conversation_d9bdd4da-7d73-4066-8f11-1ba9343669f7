@echo off
echo Installing Python PDF extraction tools...

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python first from python.org
    pause
    exit /b 1
)

echo Installing PyPDF2...
pip install PyPDF2

echo Installing pdfplumber (alternative)...
pip install pdfplumber

echo Done! You can now use the PDF extraction script.
pause
