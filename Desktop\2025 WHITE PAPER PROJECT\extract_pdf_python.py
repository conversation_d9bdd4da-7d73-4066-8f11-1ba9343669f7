#!/usr/bin/env python3
"""
PDF Text Extraction Script
Extracts text from PDF files using PyPDF2 and pdfplumber
"""

import sys
import os
from pathlib import Path

def extract_with_pypdf2(pdf_path):
    """Extract text using PyPDF2"""
    try:
        import PyPDF2
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text() + "\n"
        return text
    except ImportError:
        print("PyPDF2 not installed. Run: pip install PyPDF2")
        return None
    except Exception as e:
        print(f"PyPDF2 extraction failed: {e}")
        return None

def extract_with_pdfplumber(pdf_path):
    """Extract text using pdfplumber"""
    try:
        import pdfplumber
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        return text
    except ImportError:
        print("pdfplumber not installed. Run: pip install pdfplumber")
        return None
    except Exception as e:
        print(f"pdfplumber extraction failed: {e}")
        return None

def main():
    if len(sys.argv) < 2:
        print("Usage: python extract_pdf_python.py <pdf_file> [output_file]")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(pdf_path):
        print(f"File not found: {pdf_path}")
        sys.exit(1)
    
    print(f"Extracting text from: {pdf_path}")
    
    # Try pdfplumber first (usually better results)
    text = extract_with_pdfplumber(pdf_path)
    
    # Fallback to PyPDF2 if pdfplumber fails
    if not text:
        print("Trying PyPDF2...")
        text = extract_with_pypdf2(pdf_path)
    
    if text:
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(text)
            print(f"Text extracted to: {output_path}")
        else:
            print("Extracted text:")
            print("-" * 50)
            print(text)
    else:
        print("Failed to extract text from PDF")
        sys.exit(1)

if __name__ == "__main__":
    main()
